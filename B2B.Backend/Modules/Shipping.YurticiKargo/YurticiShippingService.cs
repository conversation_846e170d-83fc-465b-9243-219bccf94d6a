using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Shipping.Abstraction;
using Shipping.YurticiKargo.Models;
using Shipping.YurticiKargo.KOPSWebServices;
using Shipping.YurticiKargo.KOPSWebServicesTest;

namespace Shipping.YurticiKargo;

/// <summary>
/// Yurtiçi Kargo shipping service implementasyonu
/// SOLID prensiplerine uygun olarak tasarlanmış gerçek API entegrasyonu
/// </summary>
public class YurticiShippingService : IShippingService
{
    private readonly ILogger<YurticiShippingService>? _logger;
    private readonly IConfiguration _configuration;
    private readonly YurticiCarrierDefinition _definition;
    private readonly YurticiApiSettings _apiSettings;

    public YurticiShippingService(
        ILogger<YurticiShippingService>? logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _definition = new YurticiCarrierDefinition();
        _apiSettings = LoadApiSettings();
    }

    public ShippingCarrierDefinition Definition => _definition;

    /// <summary>
    /// Ortam değişkenlerinden API ayarlarını yükle
    /// </summary>
    private YurticiApiSettings LoadApiSettings()
    {
        var testMode = bool.Parse(_configuration["YURTICI_TEST_MODE"] ??
                                 _configuration["Shipping:Yurtici:TestMode"] ?? "true");

        // Test modunda sabit test bilgilerini kullan
        if (testMode)
        {
            return new YurticiApiSettings
            {
                WsUserName = "YKTEST",
                WsPassword = "YK",
                WsLanguage = "TR",
                ApiUrl = _configuration["YURTICI_API_URL"] ??
                        _configuration["Shipping:Yurtici:ApiUrl"] ??
                        "https://api.yurtici.com.tr",
                TestMode = true,
                Timeout = int.Parse(_configuration["YURTICI_TIMEOUT"] ??
                                   _configuration["Shipping:Yurtici:Timeout"] ?? "30"),
                MaxRetries = int.Parse(_configuration["YURTICI_MAX_RETRIES"] ??
                                      _configuration["Shipping:Yurtici:MaxRetries"] ?? "3")
            };
        }

        // Production modunda ortam değişkenlerinden oku
        return new YurticiApiSettings
        {
            WsUserName = _configuration["YURTICI_WS_USERNAME"] ??
                        _configuration["Shipping:Yurtici:WsUserName"] ??
                        throw new InvalidOperationException("Yurtiçi Kargo WsUserName ayarı bulunamadı"),

            WsPassword = _configuration["YURTICI_WS_PASSWORD"] ??
                        _configuration["Shipping:Yurtici:WsPassword"] ??
                        throw new InvalidOperationException("Yurtiçi Kargo WsPassword ayarı bulunamadı"),

            WsLanguage = _configuration["YURTICI_WS_LANGUAGE"] ??
                        _configuration["Shipping:Yurtici:WsLanguage"] ?? "TR",

            ApiUrl = _configuration["YURTICI_API_URL"] ??
                    _configuration["Shipping:Yurtici:ApiUrl"] ??
                    "https://api.yurtici.com.tr",

            TestMode = false,

            Timeout = int.Parse(_configuration["YURTICI_TIMEOUT"] ??
                               _configuration["Shipping:Yurtici:Timeout"] ?? "30"),

            MaxRetries = int.Parse(_configuration["YURTICI_MAX_RETRIES"] ??
                                  _configuration["Shipping:Yurtici:MaxRetries"] ?? "3")
        };
    }

    public async Task<string> CreateShipmentAsync(ShipmentRequest request)
    {
        _logger?.LogInformation("Creating Yurtiçi Kargo shipment for Order: {OrderId}", request.OrderId);

        try
        {
            // Giriş verilerini doğrula
            ValidateShipmentRequest(request);

            // Yurtiçi Kargo API isteği hazırla
            var yurticiRequest = MapToYurticiRequest(request);

            // SOAP API çağrısı yap
            var response = await CallCreateShipmentApiAsync(yurticiRequest);

            if (response.IsSuccess)
            {
                _logger?.LogInformation("Yurtiçi Kargo shipment created successfully. Tracking: {TrackingNumber}", response.CargoKey);
                return response.CargoKey ?? throw new InvalidOperationException("API'den geçerli kargo anahtarı alınamadı");
            }
            else
            {
                var errorMessage = $"Yurtiçi Kargo API Error: {response.ErrorCode} - {response.ErrorMessage}";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error creating Yurtiçi Kargo shipment for Order: {OrderId}", request.OrderId);
            throw;
        }
    }

    public async Task<ShipmentTrackingInfo> GetTrackingInfoAsync(string trackingNumber)
    {
        _logger?.LogInformation("Getting Yurtiçi Kargo tracking info for: {TrackingNumber}", trackingNumber);

        try
        {
            if (string.IsNullOrWhiteSpace(trackingNumber))
                throw new ArgumentException("Takip numarası boş olamaz", nameof(trackingNumber));

            // Yurtiçi Kargo API sorgulama isteği hazırla
            var queryRequest = new YurticiQueryShipmentRequest
            {
                Key = trackingNumber,
                KeyType = 1, // Kargo numarası
                OnlyTracking = false,
                AddHistoricalData = true
            };

            // SOAP API çağrısı yap
            var response = await CallQueryShipmentApiAsync(queryRequest);

            if (response.IsSuccess)
            {
                var trackingInfo = MapToTrackingInfo(response, trackingNumber);
                _logger?.LogInformation("Yurtiçi Kargo tracking info retrieved successfully for: {TrackingNumber}", trackingNumber);
                return trackingInfo;
            }
            else
            {
                var errorMessage = $"Yurtiçi Kargo API Error: {response.ErrorCode} - {response.ErrorMessage}";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting Yurtiçi Kargo tracking info for: {TrackingNumber}", trackingNumber);
            throw;
        }
    }

    public async Task<bool> CancelShipmentAsync(string trackingNumber)
    {
        _logger?.LogInformation("Cancelling Yurtiçi Kargo shipment: {TrackingNumber}", trackingNumber);

        try
        {
            if (string.IsNullOrWhiteSpace(trackingNumber))
                throw new ArgumentException("Takip numarası boş olamaz", nameof(trackingNumber));

            // Yurtiçi Kargo API'sinde iptal işlemi için özel endpoint kullanılır
            // Bu implementasyon API dokümantasyonuna göre güncellenmelidir
            var success = await CallCancelShipmentApiAsync(trackingNumber);

            if (success)
            {
                _logger?.LogInformation("Yurtiçi Kargo shipment cancelled successfully: {TrackingNumber}", trackingNumber);
                return true;
            }
            else
            {
                _logger?.LogWarning("Yurtiçi Kargo shipment cancellation failed: {TrackingNumber}", trackingNumber);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error cancelling Yurtiçi Kargo shipment: {TrackingNumber}", trackingNumber);
            return false;
        }
    }

    public async Task<bool> ValidateSettingsAsync(Dictionary<string, string> settings)
    {
        try
        {
            // Gerekli ayarları kontrol et
            if (!settings.ContainsKey("WsUserName") || string.IsNullOrWhiteSpace(settings["WsUserName"]))
                return false;

            if (!settings.ContainsKey("WsPassword") || string.IsNullOrWhiteSpace(settings["WsPassword"]))
                return false;

            // Test API çağrısı yaparak ayarları doğrula
            var testSettings = new YurticiApiSettings
            {
                WsUserName = settings["WsUserName"],
                WsPassword = settings["WsPassword"],
                WsLanguage = settings.GetValueOrDefault("WsLanguage", "TR"),
                ApiUrl = settings.GetValueOrDefault("ApiUrl", "https://api.yurtici.com.tr"),
                TestMode = bool.Parse(settings.GetValueOrDefault("TestMode", "true")),
                Timeout = int.Parse(settings.GetValueOrDefault("Timeout", "30"))
            };

            // Basit bir test sorgusu yap
            return await TestApiConnectionAsync(testSettings);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error validating Yurtiçi Kargo settings");
            return false;
        }
    }

    public Task<decimal?> CalculateShippingCostAsync(ShipmentRequest request)
    {
        _logger?.LogInformation("Calculating Yurtiçi Kargo shipping cost for Order: {OrderId}", request.OrderId);

        try
        {
            // Basit maliyet hesaplama (gerçek API'de daha karmaşık olacak)
            decimal baseCost = 15.00m; // Temel ücret
            decimal weightCost = request.Weight * 2.50m; // Ağırlık başına ücret

            // Şehir bazlı ek ücret (örnek)
            decimal cityCost = GetCityCost(request.City);

            decimal totalCost = baseCost + weightCost + cityCost;

            // Minimum ücret kontrolü
            if (totalCost < 10.00m)
                totalCost = 10.00m;

            _logger?.LogInformation("Yurtiçi Kargo shipping cost calculated: {Cost} for Order: {OrderId}", totalCost, request.OrderId);

            return Task.FromResult<decimal?>(totalCost);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calculating Yurtiçi Kargo shipping cost for Order: {OrderId}", request.OrderId);
            return Task.FromResult<decimal?>(null);
        }
    }

    #region Private Methods

    /// <summary>
    /// Kargo oluşturma isteğini doğrula
    /// </summary>
    private static void ValidateShipmentRequest(ShipmentRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        if (string.IsNullOrWhiteSpace(request.RecipientName))
            throw new ArgumentException("Alıcı adı boş olamaz", nameof(request.RecipientName));

        if (string.IsNullOrWhiteSpace(request.RecipientPhone))
            throw new ArgumentException("Alıcı telefonu boş olamaz", nameof(request.RecipientPhone));

        if (string.IsNullOrWhiteSpace(request.Address))
            throw new ArgumentException("Adres boş olamaz", nameof(request.Address));

        if (string.IsNullOrWhiteSpace(request.City))
            throw new ArgumentException("Şehir boş olamaz", nameof(request.City));

        if (request.Weight <= 0)
            throw new ArgumentException("Ağırlık 0'dan büyük olmalıdır", nameof(request.Weight));
    }

    /// <summary>
    /// ShipmentRequest'i Yurtiçi API formatına dönüştür
    /// </summary>
    private static YurticiCreateShipmentRequest MapToYurticiRequest(ShipmentRequest request)
    {
        // Benzersiz kargo anahtarı oluştur
        var cargoKey = $"YK{DateTime.Now:yyyyMMddHHmmss}{Random.Shared.Next(1000, 9999)}";
        var invoiceKey = $"INV{request.OrderId.ToString("N")[..8].ToUpper()}";

        // Telefon numarasını formatla (0 ile başlamalı)
        var phone = request.RecipientPhone.Trim();
        if (!phone.StartsWith("0"))
            phone = "0" + phone;

        return new YurticiCreateShipmentRequest
        {
            CargoKey = cargoKey,
            InvoiceKey = invoiceKey,
            ReceiverCustName = request.RecipientName.Trim(),
            ReceiverAddress = $"{request.Address}, {request.District}/{request.City}".Trim(),
            ReceiverPhone1 = phone,
            ReceiverEmail = request.RecipientEmail,
            DeclaredValue = request.DeclaredValue,
            Weight = request.Weight,
            SpecialInstructions = request.SpecialInstructions
        };
    }

    /// <summary>
    /// Yurtiçi API yanıtını TrackingInfo'ya dönüştür
    /// </summary>
    private static ShipmentTrackingInfo MapToTrackingInfo(YurticiQueryShipmentResponse response, string trackingNumber)
    {
        return new ShipmentTrackingInfo
        {
            TrackingNumber = trackingNumber,
            Status = MapYurticiStatusToStandard(response.Status),
            StatusDescription = response.StatusDescription ?? "Durum bilgisi alınamadı",
            LastUpdate = response.LastUpdate ?? DateTime.UtcNow,
            EstimatedDelivery = response.EstimatedDelivery,
            CurrentLocation = response.CurrentLocation ?? "Bilinmiyor",
            TrackingEvents = new List<TrackingEvent>
            {
                new()
                {
                    EventDate = response.LastUpdate ?? DateTime.UtcNow,
                    Description = response.StatusDescription ?? "Durum güncellendi",
                    Location = response.CurrentLocation ?? "Yurtiçi Kargo"
                }
            }
        };
    }

    /// <summary>
    /// Yurtiçi Kargo durumunu standart duruma dönüştür
    /// </summary>
    private static string MapYurticiStatusToStandard(string? yurticiStatus)
    {
        return yurticiStatus?.ToLowerInvariant() switch
        {
            "alindi" or "kargo_alindi" => "Picked",
            "yolda" or "transfer_merkezi" => "InTransit",
            "dagitimda" or "dagitim_merkezi" => "OutForDelivery",
            "teslim_edildi" or "teslim" => "Delivered",
            "iptal" or "iptal_edildi" => "Cancelled",
            "iade" or "iade_edildi" => "Returned",
            _ => "InTransit"
        };
    }

    /// <summary>
    /// Şehir bazlı ek maliyet hesapla
    /// </summary>
    private static decimal GetCityCost(string city)
    {
        // Büyük şehirler için ek ücret yok
        var majorCities = new[] { "İSTANBUL", "ANKARA", "İZMİR", "BURSA", "ANTALYA" };

        if (majorCities.Contains(city.ToUpperInvariant()))
            return 0m;

        // Diğer şehirler için ek ücret
        return 5.00m;
    }

    /// <summary>
    /// Yurtiçi Kargo SOAP servisine kargo oluşturma çağrısı yap
    /// </summary>
    private async Task<YurticiCreateShipmentResponse> CallCreateShipmentApiAsync(YurticiCreateShipmentRequest request)
    {
        try
        {
            if (_apiSettings.TestMode)
            {
                return await CallCreateShipmentTestApiAsync(request);
            }
            else
            {
                return await CallCreateShipmentProductionApiAsync(request);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo createShipment SOAP API");
            return new YurticiCreateShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Test ortamı için kargo oluşturma çağrısı
    /// </summary>
    private async Task<YurticiCreateShipmentResponse> CallCreateShipmentTestApiAsync(YurticiCreateShipmentRequest request)
    {
        _logger?.LogInformation("Using TEST environment for Yurtiçi Kargo createShipment with credentials: {Username}", _apiSettings.WsUserName);

        // Test ortamı için KOPSWebServicesTest client kullan
        using var client = new KOPSWebServicesTest.ShippingOrderDispatcherServicesClient();

        // Test client'ı da aynı KOPSWebServices namespace'ini kullanıyor
        var createShipmentRequest = new KOPSWebServices.createShipment
        {
            wsUserName = "YKTEST", // "YKTEST"
            wsPassword = "YK", // "YK"
            userLanguage = "TR", // "TR"
            ShippingOrderVO = new KOPSWebServices.ShippingOrderVO[]
            {
                new KOPSWebServices.ShippingOrderVO
                {
                    cargoKey = request.CargoKey,
                    invoiceKey = request.InvoiceKey,
                    receiverCustName = request.ReceiverCustName,
                    receiverAddress = request.ReceiverAddress,
                    receiverPhone1 = request.ReceiverPhone1
                }
            }
        };

        var response = await client.createShipmentAsync(createShipmentRequest);
        return ParseCreateShipmentResponse(response); // Aynı parse metodu kullanılabilir
    }

    /// <summary>
    /// Canlı ortam için kargo oluşturma çağrısı
    /// </summary>
    private async Task<YurticiCreateShipmentResponse> CallCreateShipmentProductionApiAsync(YurticiCreateShipmentRequest request)
    {
        _logger?.LogInformation("Using PRODUCTION environment for Yurtiçi Kargo createShipment");

        // Canlı ortam için KOPSWebServices client kullan
        using var client = new KOPSWebServices.ShippingOrderDispatcherServicesClient();

        var createShipmentRequest = new KOPSWebServices.createShipment
        {
            wsUserName = _apiSettings.WsUserName,
            wsPassword = _apiSettings.WsPassword,
            userLanguage = _apiSettings.WsLanguage,
            ShippingOrderVO = new KOPSWebServices.ShippingOrderVO[]
            {
                new KOPSWebServices.ShippingOrderVO
                {
                    cargoKey = request.CargoKey,
                    invoiceKey = request.InvoiceKey,
                    receiverCustName = request.ReceiverCustName,
                    receiverAddress = request.ReceiverAddress,
                    receiverPhone1 = request.ReceiverPhone1
                }
            }
        };

        var response = await client.createShipmentAsync(createShipmentRequest);
        return ParseCreateShipmentResponse(response);
    }

    /// <summary>
    /// Yurtiçi Kargo SOAP servisine kargo sorgulama çağrısı yap
    /// </summary>
    private async Task<YurticiQueryShipmentResponse> CallQueryShipmentApiAsync(YurticiQueryShipmentRequest request)
    {
        try
        {
            if (_apiSettings.TestMode)
            {
                return await CallQueryShipmentTestApiAsync(request);
            }
            else
            {
                return await CallQueryShipmentProductionApiAsync(request);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo queryShipment SOAP API");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Test ortamı için kargo sorgulama çağrısı
    /// </summary>
    private async Task<YurticiQueryShipmentResponse> CallQueryShipmentTestApiAsync(YurticiQueryShipmentRequest request)
    {
        _logger?.LogInformation("Using TEST environment for Yurtiçi Kargo queryShipment");

        // Test ortamı için KOPSWebServicesTest client kullan
        using var client = new KOPSWebServicesTest.ShippingOrderDispatcherServicesClient();

        // Test client'ı da aynı KOPSWebServices namespace'ini kullanıyor
        var queryShipmentRequest = new KOPSWebServices.queryShipment
        {
            wsUserName = "YKTEST", // "YKTEST"
            wsPassword = "YK", // "YK"
            wsLanguage = "TR", // "TR"
            keyType = request.KeyType,
            keys = new string[] { request.Key },
            onlyTracking = request.OnlyTracking,
            addHistoricalData = request.AddHistoricalData
        };

        var response = await client.queryShipmentAsync(queryShipmentRequest);
        return ParseQueryShipmentResponse(response); // Aynı parse metodu kullanılabilir
    }

    /// <summary>
    /// Canlı ortam için kargo sorgulama çağrısı
    /// </summary>
    private async Task<YurticiQueryShipmentResponse> CallQueryShipmentProductionApiAsync(YurticiQueryShipmentRequest request)
    {
        _logger?.LogInformation("Using PRODUCTION environment for Yurtiçi Kargo queryShipment");

        // Canlı ortam için KOPSWebServices client kullan
        using var client = new KOPSWebServices.ShippingOrderDispatcherServicesClient();

        var queryShipmentRequest = new KOPSWebServices.queryShipment
        {
            wsUserName = _apiSettings.WsUserName,
            wsPassword = _apiSettings.WsPassword,
            wsLanguage = _apiSettings.WsLanguage,
            keyType = request.KeyType,
            keys = new string[] { request.Key },
            onlyTracking = request.OnlyTracking,
            addHistoricalData = request.AddHistoricalData
        };

        var response = await client.queryShipmentAsync(queryShipmentRequest);
        return ParseQueryShipmentResponse(response);
    }

    /// <summary>
    /// Yurtiçi Kargo API'sine kargo iptal çağrısı yap
    /// </summary>
    private async Task<bool> CallCancelShipmentApiAsync(string trackingNumber)
    {
        try
        {
            // Yurtiçi Kargo API'sinde iptal işlemi için özel endpoint kullanılır
            // Bu implementasyon API dokümantasyonuna göre güncellenmelidir
            _logger?.LogWarning("Yurtiçi Kargo cancel shipment API not implemented yet for: {TrackingNumber}", trackingNumber);

            // Şimdilik false döndür - gerçek implementasyon eklenecek
            await Task.Delay(100); // Simüle edilmiş gecikme
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo cancel API for: {TrackingNumber}", trackingNumber);
            return false;
        }
    }

    /// <summary>
    /// API bağlantısını test et
    /// </summary>
    private async Task<bool> TestApiConnectionAsync(YurticiApiSettings settings)
    {
        try
        {
            // Basit bir test sorgusu yap
            var testRequest = new YurticiQueryShipmentRequest
            {
                Key = "TEST123",
                KeyType = 1,
                OnlyTracking = true,
                AddHistoricalData = false
            };

            var response = await CallQueryShipmentApiAsync(testRequest);

            // API'ye ulaşabiliyorsak (hata kodu EXCEPTION değilse) bağlantı başarılı
            return response.ErrorCode != "EXCEPTION";
        }
        catch
        {
            return false;
        }
    }





    /// <summary>
    /// Canlı ortam kargo oluşturma SOAP yanıtını parse et
    /// </summary>
    private YurticiCreateShipmentResponse ParseCreateShipmentResponse(KOPSWebServices.createShipmentResponse1 soapResponse)
    {
        try
        {
            var response = new YurticiCreateShipmentResponse();

            if (soapResponse?.createShipmentResponse?.ShippingOrderResultVO?.shippingOrderDetailVO != null)
            {
                var orderDetail = soapResponse.createShipmentResponse.ShippingOrderResultVO.shippingOrderDetailVO.FirstOrDefault();

                if (orderDetail != null)
                {
                    response.CargoKey = orderDetail.cargoKey;
                    response.InvoiceKey = orderDetail.invoiceKey;
                    response.ErrorCode = orderDetail.errCode.ToString();
                    response.ErrorMessage = orderDetail.errMessage;
                    // operationMessage ve operationStatus alanları bu modelde yok
                    response.OperationMessage = string.IsNullOrEmpty(orderDetail.errMessage) ? "Success" : orderDetail.errMessage;
                    response.OperationStatus = orderDetail.errCode == 0 ? "Success" : "Error";
                }
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error parsing Yurtiçi Kargo createShipment SOAP response");
            return new YurticiCreateShipmentResponse
            {
                ErrorCode = "PARSE_ERROR",
                ErrorMessage = $"Response parsing error: {ex.Message}"
            };
        }
    }



    /// <summary>
    /// Canlı ortam kargo sorgulama SOAP yanıtını parse et
    /// </summary>
    private YurticiQueryShipmentResponse ParseQueryShipmentResponse(KOPSWebServices.queryShipmentResponse1 soapResponse)
    {
        try
        {
            var response = new YurticiQueryShipmentResponse();

            if (soapResponse?.queryShipmentResponse?.ShippingDeliveryVO?.shippingDeliveryDetailVO != null)
            {
                var deliveryDetail = soapResponse.queryShipmentResponse.ShippingDeliveryVO.shippingDeliveryDetailVO.FirstOrDefault();

                if (deliveryDetail != null)
                {
                    response.ErrorCode = deliveryDetail.errCode.ToString();
                    response.ErrorMessage = deliveryDetail.errMessage;
                    response.InvoiceKey = deliveryDetail.invoiceKey;
                    response.OperationMessage = deliveryDetail.operationMessage;
                    response.OperationStatus = deliveryDetail.operationStatus;

                    // Kargo detayları
                    if (deliveryDetail.shippingDeliveryItemDetailVO != null)
                    {
                        var itemDetail = deliveryDetail.shippingDeliveryItemDetailVO;
                        response.CargoKey = itemDetail.docId;
                        response.TrackingUrl = itemDetail.trackingUrl;

                        // Durum bilgilerini çıkar
                        var statusText = itemDetail.cargoEventExplanation ?? itemDetail.deliveryTypeExplanation ?? "Kargo işlemde";
                        response.Status = MapYurticiStatusToStandard(statusText);
                        response.StatusDescription = statusText;

                        // Tarih bilgisi
                        if (DateTime.TryParse(itemDetail.deliveryDate, out var deliveryDate))
                            response.LastUpdate = deliveryDate;
                        else
                            response.LastUpdate = DateTime.UtcNow;

                        // Konum bilgisi
                        response.CurrentLocation = itemDetail.deliveryUnitName ?? itemDetail.arrivalUnitName ?? "Yurtiçi Kargo";
                    }
                }
            }

            // Hata durumunda varsayılan değerler
            if (string.IsNullOrEmpty(response.Status))
            {
                response.Status = "InTransit";
                response.StatusDescription = "Kargo takip edilebilir durumda";
                response.LastUpdate = DateTime.UtcNow;
                response.CurrentLocation = "Yurtiçi Kargo";
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error parsing Yurtiçi Kargo queryShipment SOAP response");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "PARSE_ERROR",
                ErrorMessage = $"Response parsing error: {ex.Message}"
            };
        }
    }

    #endregion
}
