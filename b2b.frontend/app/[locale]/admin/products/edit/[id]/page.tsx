import { Suspense } from "react";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import ProductEditServer from "./components/ProductEditServer";
import PageHeaderServer from "../../../components/PageHeaderServer";
import { Skeleton } from "@/components/ui/skeleton";


interface ProductEditPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

export async function generateMetadata({ params }: ProductEditPageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "product" });

  return {
    title: `${t("editProduct")} | Admin`,
    description: t("editProductDescription"),
  };
}

export default async function ProductEditPage({ params }: ProductEditPageProps) {
  const { id } = await params;

  if (!id) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeaderServer
        title="<PERSON><PERSON><PERSON><PERSON>"
        description="Mevcut ürünü düzenleyin ve güncelleyin"
        actions={[
          {
            actionLabel: "Ürünlere Dön",
            actionVariant: "outline",
            actionUrl: "/admin/products",
            actionIcon: "back"
          },
          {
            actionLabel: "Yeni Ürün Ekle",
            actionVariant: "default",
            actionUrl: "/admin/products/add",
            actionIcon: "add"
          }
        ]}
      />

      <Suspense fallback={<ProductEditSkeleton />}>
        <ProductEditServer productId={id} />
      </Suspense>
    </div>
  );
}

function ProductEditSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <div className="space-y-2">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-full" />
            ))}
          </div>
        </div>
        <div className="lg:col-span-3">
          <div className="space-y-6">
            <Skeleton className="h-96 w-full" />
            <div className="flex justify-end space-x-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
